<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lattice - Where Marketing Careers are Build</title>
    <link rel="stylesheet" href="exercise4.css">
   
</head>
<body>
    <header>
        <div class="logo">
            <svg viewBox="0 0 84 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                
            </svg>
        </div>
        
        <div class="main-nav">
            <div class="nav-links">
                <a href="#">Product</a>
                <a href="#">Services</a>
                <a href="#">Career</a>
                <a href="#">Pricing</a>
                <a href="#">About</a>
            </div>
            
            <a href="#" class="login-btn">Login</a>
            
            <div class="search-icon">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M11 19C15.4183 19 19 15.4183 19 11C19 6.58172 15.4183 3 11 3C6.58172 3 3 6.58172 3 11C3 15.4183 6.58172 19 11 19Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M21 21L16.65 16.65" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </div>
            
            <button class="menu-btn">
                Menu
                
                </svg>
            </button>
        </div>
    </header>
    
    <section class="hero">
        <h1>Where Marketing Careers are Build.</h1>
        <p>We bring ideas to life by nurturing talent of experienced at our very talented team.</p>
        
        <div class="cta-buttons">
            <a href="#" class="primary-btn">Build career</a>
            <a href="#" class="secondary-btn">
                Contact us
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M5 12H19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M12 5L19 12L12 19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </a>
        </div>
    </section>
    
    <section class="feature-section">
        <div class="feature-tabs">
            <button class="tab active">Building career</button>
        </div>
        
        <div class="feature-content">
            <div class="feature-text">
                <span>All-in-one platform</span>
                <h2>Efficiently transform your candidate experience.</h2>
                <p>Simple tools to help you acquire, engage and develop the talent you need to provide a seamless candidate experience.</p>
            </div>
            
            <div class="feature-image"></div>
        </div>
        
        <div class="profile-section">
            <div class="profile">
                <img src="download.png" alt="">
                <div class="profile-text">
                    <div class="name">Fred Miller</div>
                    <div class="title">Marketing Director</div>
                </div>
            </div>
            
            <div class="profile">
                <img src="download.png" alt="">
                <div class="profile-text">
                    <div class="name">Marlon Stevens</div>
                    <div class="title">Brand Manager</div>
                </div>
            </div>
            
            <div class="profile">
                <img src="download.png" alt="">
                <div class="profile-text">
                    <div class="name">Donald Richards</div>
                    <div class="title">Marketing Strategist</div>
                </div>
            </div>
            
            <div class="profile">
                <img src="download.png" alt="">
                <div class="profile-text">
                    <div class="name">Eleanor Pena</div>
                    <div class="title">Content Director</div>
                </div>
            </div>
        </div>
        
        <div href="#" class="learn-more">
            Learn more
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M5 12H19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M12 5L19 12L12 19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
        </div>
    </section>


    <p>Instruction for  life</p>
   <ul>
      <li>Eat</li>
      <li>Sleep</li>
      <li>Learn</li>
      <li>Repeat</li>
   </ul>
 <script src="index.js"></script>
</body>
</html>