         *  { margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        
        body {
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }
        
        
        header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            background-color: white;
        }
        
        .logo {
            display: flex;
            align-items: center;
        }
        
        .logo svg {
            height: 20px;
        }
        
        .main-nav {
            display: flex;
            gap: 20px;
            align-items: center;
        }
        
        .nav-links {
            display: flex;
            gap: 20px;
        }
        
        .nav-links a {
            text-decoration: none;
            color: #333;
            font-size: 14px;
        }
        
        .search-icon {
            margin-left: 20px;
        }
        
        .login-btn {
            background-color: #000;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            margin-right: 8px;
        }
        
        .menu-btn {
            display: flex;
            align-items: center;
            gap: 5px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 14px;
        }
        
        
        .hero {
            text-align: center;
            padding: 60px 20px;
            max-width: 800px;
            margin: 0 auto;
        }
        
        .hero h1 {
            font-size: 42px;
            font-weight: 700;
            margin-bottom: 20px;
            line-height: 1.2;
        }
        
        .hero p {
            font-size: 16px;
            color: #666;
            margin-bottom: 30px;
        }
        
        .cta-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 40px;
        }
        
        .primary-btn {
            background-color: #000;
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 500;
            display: flex;
            align-items: center;
        }
        
        .secondary-btn {
            display: flex;
            align-items: center;
            gap: 5px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 14px;
            color: #333;
            text-decoration: none;
        }
        
        .feature-section {
            background-color: #e8e1db;
            border-radius: 15px;
            padding: 30px;
            max-width: 1000px;
            margin: 0 auto 60px;
            position: relative;
            overflow: hidden;
        }
        
        .feature-tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .tab {
            background-color: rgba(255, 255, 255, 0.5);
            border: none;
            border-radius: 15px;
            padding: 8px 15px;
            font-size: 13px;
            cursor: pointer;
        }
        
        .tab.active {
            background-color: white;
            font-weight: 500;
        }
        
        .feature-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }
        
        .feature-text {
            padding-top: 30px;
        }
        
        .feature-text span {
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 1px;
            color: #666;
        }
        
        .feature-text h2 {
            font-size: 28px;
            margin: 15px 0;
            line-height: 1.3;
            font-weight: 600;
        }
        
        .feature-text p {
            font-size: 14px;
            color: #666;
            margin-bottom: 20px;
        }
        
        .feature-image {
            background-image: url('images/T08QH5UE0DD-U08QH5UE0KH-660c6bcb13fb-512.jpeg');
            background-size: cover;
            background-position: center;
            border-radius: 10px;
            min-height: 300px;
        }
        
        .learn-more {
            position: absolute;
            bottom: 20px;
            right: 20px;
            background-color: white;
            border-radius: 20px;
            padding: 8px 15px;
            display: flex;
            align-items: center;
            gap: 5px;
            text-decoration: none;
            color: #333;
            font-size: 13px;
        }
        
        /* Profile section */
        .profile-section {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            justify-content: center;
            margin: 30px 0;
        }
        
        .profile {
            display: flex;
            align-items: center;
            gap: 8px;
            background-color: white;
            border-radius: 25px;
            padding: 6px 15px;
        }
        
        .profile img {
            width: 24px;
            height: 24px;
            border-radius: 50%;
        }
        
        .profile-text {
            font-size: 12px;
        }
        
        .profile-text .name {
            font-weight: 500;
        }
        
        .profile-text .title {
            color: #777;
            font-size: 11px;
        }
        .done{
            color:darkgrey;
            text-decoration: line-through solid black 2px;
        }