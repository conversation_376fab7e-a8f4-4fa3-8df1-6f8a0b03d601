body{
    background-color:#ece2e0;
    
}
.nav-bar{
    background-color:#ece2e0;
    color: #ffffff;
    margin-left: 9rem;
    display: flex;
    align-items: center;
    margin-top: 1rem;
}
.nav-bar ul{
    list-style: none;
    display: flex;
    margin-top: 0.1rem;
    gap: 0;
    
    
}
.nav-bar a{
color: black;
text-decoration: none;
padding-right: 8rem;
font-size: 20px;
align-items: center;
margin-bottom: 0;
font-weight: 500;

}
.btn{
    position: absolute;
    right: 2rem;
    background-color: #762adc;
    height: 2rem;
    width: 8rem;
    border-radius: 0.5rem;
    border: none;
    color: white;
    font-size: 1rem;
    cursor: pointer;
    
}
.logo{
    display: flex;
    color: black;
    font-size: 36px;
    font-weight: 700;
    margin-left: 1rem;
    margin-right: 1rem;     
    align-items: center;
    margin-top: 0.5rem;

}
 .container{
    display: flex;
    flex-direction: row;
    margin-top: 5rem;     
 }
 .container-1{
    padding-left: 10rem;
    width: 50rem;
    color: 20px;
    font-size: 20px;
 }
 .pro-btn{
    background-color: dark;
    color: white;
    font-size: 12px;
    padding-left: 2.5rem;
    padding-right: 2.5rem;
    height: 2rem;
    border-radius: 4px;
    margin-right: 5rem;
 }
 .here-btn{
     background-color: 
#ece2e0;
border-width: 1px;
border-color: #762adc;
border-radius: 4px;
height: 2rem;
padding-left: 3rem;
padding-right: 3rem;
color: #762adc;
font-family: bold;
 }
 span{
    color:#762adc;
 }
 .title{
    font-size: 3rem;
    font-weight: bold;
 }