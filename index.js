// //Assignment 
// //Quetion 1
// let name ="<PERSON> ";
// let age = 17;
// let invitation = true;
// if(age >= 18 && invitation == true){
//     console.log(name+ "You are invited to the party");
// }else if(age < 18 && invitation == true){
//     console.log(name+ "You are not invited to the party");
// }else if(age >= 18 && invitation == false){
//     console.log(name + " You are not invited to the party");
// }else{
//     console.log(name+ "You are not invited to the party");
// }

// let myName = "Enoch Sakyi";
// let myAge = 45;
// let school = "Takoradi Technical University";
// let favColor = "White";

// console.log("My name is "+ myName + " I am " + myAge + " years old. I study at " + school + " and my favorite color is " + favColor);


// let x = 10;
// let y  = 5;
// let sum = x + y;
// console.log("The sum is "+sum);

// x= 20;
//  y = 10;
// let difference = x - y;
// console.log("The difference is "+difference);

// y = 5;
// x = 10;
// let product = x * y;
// console.log("The product is "+product);

// x = 20;
// y = 10;
// let quotient = x / y;
// console.log(" The quotient is "+quotient);

// x = 10;
// y  = 5;
// let modulus = x % y;
// console.log("The modulus is "+modulus);



// let CurrentYear = 2025;
// let MyAge = 23;
// let myAge1 = CurrentYear - MyAge;
// console.log(myAge1);


// console.log(document.querySelectorAll("li")[0].style.color = "red");
// console.log(document.querySelectorAll("li")[1].hidden = true);
// console.log(document.querySelectorAll("li")[2].style.fontSize = "50px");
// console.log(document.querySelectorAll("li")[3].style.fontSize= "10px");
// document.querySelectorAll('li')[0].addEventListener('click', ()=>(document.querySelectorAll('li')[0].style.color = "red"));

// list = document.querySelectorAll('li');
// list.forEach(element => {
//     if (element.style.color == "black") {
//         element.addEventListener('click', () => (element.style.color = "red"));
//     }
//     else{
//         element.addEventListener('click', () => (element.style.color = "blue"));
//     }
// });


// list = document.querySelectorAll('li');
// list.forEach(element => {
//     element.addEventListener('click', () => {
//         if (element.style.color === "red" ) {
//             element.style.color = "black";
//         } 
//             element.style.color = element.style.color === "black" ? "red" : "blue";
//     });
// });



// function sayHello(name="Roger ", response){
    //     console.log("Hello "+ name, "Did you know United is the best team "+ response);
    // }
    // sayHello();
    
    // function Sum (x, y){
    // sum = x+y;
    // console.log(sum);
    // }
    
    // Sum(33,99);
    // Sum(2,8);
    // addTwo =(num1, num2)=>{
    //     console.log(num1,num2);
    // }
    // addTwo(3,6);


    // function nameString (fullname, surname){
    //     let SayName = fullname + " " + surname;
    //     return SayName;
    // }
    // let tempname  = nameString("Roger","Perry");
    
    // function Hello (){
    //     console.log("Hello "+tempname)
    // }
    // Hello();

//     //Question 1
//     function welcomeMessage(){
//         console.log("Welcome to the world of JavaScript!");
//     }
//     welcomeMessage();



//     //Question 2
//      function squareNumber (x){
//     return x*x;
//     }
//     console.log(squareNumber(5));

    
//     //Question 3
//     function checkEvenOdd(x){
//        if(x%2==0){
//         console.log("The number is Even number");
//        }
//        else{
//         console.log("The number is an Odd number");
//        }
//     }
//     checkEvenOdd(5);

//     //Question 4
//     function addNumbers (x, y){
//     sum = x+y;
//     console.log(sum);
//     }
    
//     addNumbers(33,99);

//     //Question 5
//     function greetUser (fullname, surname){
//         let SayName = fullname + " " + surname;
//         return SayName;
//     }
//     let tempname  = greetUser("Roger","Perry");
    
//     function Hello (){
//         console.log("Hello "+tempname)
//     }
//     Hello();

//     //Question 6
//     function test() {
//     return "Done!"; // The function returns here, so the next line won't execute
//     console.log("This will not run"); // This line is unreachable and will never execute
// }


// //Question 
// function convertToMinutes(hours) {
//     return hours * 60;
// }
// console.log(convertToMinutes(2));



// function calculateSpeed(time){
//     let distance = 30;
//     let speed = distance/time;
//     return speed;
// }
// console.log(calculateSpeed(2));



// let randomArray = [1, "Hello", true, false, undefined];
// console.log(randomArray);

// for (let i = 0; i < randomArray.length; i++) {
//     console.log(randomArray[i]);
// }

// // Find the first element that is a string
// let firstString = randomArray.find(element => typeof element === "string");
// console.log("First string in array:", firstString);


// let numberArray = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
// console.log(numberArray);

// // Find the first number greater than 5
// let firstGreaterThanFive = numberArray.find(num => num > 5);
// console.log("First number greater than 5:", firstGreaterThanFive);

// let number1Array = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
// console.log(numberArray);

// // Find the first even number in the array
// let firstEven = numberArray.find(num => num % 2 === 0);
// console.log("First even number in array:", firstEven);



// const fruits = ["apple", "banana", "cherry", "date", "elderberry"];

// let evenIndexArray = fruits.filter((fruits,index) =>{
//     return index % 2 === 0;
// })
// console.log(evenIndexArray);

// let club = {
//     clubName : "Manchester United",
//     clubColor : "Red",
//     clubLocation : "Old Trafford",
//     clubManager : "Rueben Amorim",
    
// }

// club = {
//     ...club,
//     clubposition : 10
// }


// console.log(club);
// delete club.clubposition;
// console.log(club);



// let students = {
//     age: 20,
//     name :"Chess",
//     subjects :["Math","Science","History"],
//     address:{
//         city: "New York",
//         zip: "10001",
//     },


// }


// let dataComingFromApi =[
//     {
//     id : 1,
//     age:25,
//     name :"Charlie"
//     },
//     {
//         id : 2,
//     age:30,
//     name :"John"
//     },
//     {
//         id : 3,
//     age:25,
//     name :"Iane"
//     }
//     ,
//     {
//         id : 4,
//     age:30,
//     name :"John"
//     },
//     {
//         id : 5,
//     age:25,
//     name :"Roger"
//     }

// ];

// dataComingFromApi.forEach((data)=>{
//     data.name = "Kelvin"
// console.log(data)

// });
// cloneArray = dataComingFromApi.map((item)=>
// {
// return (item = {...item, name: "Kelvin"})
// }
// );
// console.log(dataComingFromApi)
// console.log(cloneArray);































