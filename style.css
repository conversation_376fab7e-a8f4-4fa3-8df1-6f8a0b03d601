body{
    background-color:rgb(27, 23, 23);
   
}
.top-element{
    position: absolute;
    right: 0; 
    color: white;
    margin-right: 0.5rem;
    margin-top: 0.5rem;
    justify-content: space-between;
 }
 .mid-div{
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    flex-direction: column;
 }
 .mid-div-text{
    color: white;
    font-size: 8em;
    font-weight: 300;
    padding-bottom: 0rem;
    margin-bottom: 0rem;
 }
 .mid-div-search{
   margin-top: 1rem;
    border-style: none;
    border-radius: 1rem;
    height: 3rem;
    width: 50rem;
    margin-bottom: 2rem;
    padding-left: 2rem;
    font-size: 15px;
    
 }
 .images-container{
   display: flex;
   flex-direction: row;
   align-items: center;
   justify-items: center;
   flex-wrap: wrap;
 }
 .images{
   padding: 0.5rem;
   height: 5rem;
   width: 5rem;
   margin-right: 1rem;
   border-radius: 50%;
   
 }
 .bottom-div{
   display: flex;
   flex-direction: row;
   justify-content: end;
   justify-content: end;
 }
 .btn{
   background-color: rgb(59, 59, 184);
   width: 10rem;
   height:2rem;
   border-radius: 1rem;
   margin-bottom: 1rem;
   margin-right: 1rem;
   color: white;
   border-style: none;
   

 }