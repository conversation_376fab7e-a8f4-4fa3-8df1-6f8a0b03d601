<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Responsive Navigation Menu</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: Arial, sans-serif;
        }
        
        body {
            background-color: #f5f5f5;
        }
        
        nav {
            background-color: rgb(190, 29, 29);
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            color: white;
            width: 100%;
        }
        
        .logo {
            height: 2rem;
            width: 2rem;
            object-fit: contain;
            border-radius: 1rem;
        }
        
        ul {
            list-style: none;
            display: flex;
            justify-content: center;
            gap: 30px;
            transition: all 0.3s ease;
        }
        
        li {
            font-weight: bold;
            cursor: pointer;
            transition: color 0.3s ease;
            padding: 5px 10px;
        }
        
        li:hover {
            color: rgb(226, 130, 5);
        }
        
        #menu {
            display: none;
        }
        
        .hamburger {
            display: none;
            cursor: pointer;
            flex-direction: column;
            justify-content: center;
            z-index: 2;
            width: 30px;
            height: 25px;
        }
        
        .hamburger span {
            width: 30px;
            height: 3px;
            background-color: white;
            margin: 3px 0;
            transition: 0.4s;
            border-radius: 2px;
        }
        
        .hamburger:hover span {
            background-color: rgb(226, 130, 5);
        }
        
        /* Animation for hamburger to X */
        #menu:checked + label span:nth-child(1) {
            transform: rotate(45deg) translate(5px, 5px);
        }
        
        #menu:checked + label span:nth-child(2) {
            opacity: 0;
        }
        
        #menu:checked + label span:nth-child(3) {
            transform: rotate(-45deg) translate(7px, -6px);
        }
        
        .content {
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .img {
            max-width: 100%;
            height: auto;
            display: block;
            margin: 20px 0;
        }
        
        /* Responsive styles */
        @media (max-width: 768px) {
            .hamburger {
                display: flex;
            }
            
            ul {
                display: none;
                flex-direction: column;
                position: absolute;
                top: 60px;
                left: 0;
                right: 0;
                background-color: rgb(226, 130, 5);
                padding: 20px;
                box-shadow: 0 5px 10px rgba(0,0,0,0.1);
                z-index: 1;
            }
            
            ul li {
                padding: 12px 20px;
                border-bottom: 1px solid rgba(255,255,255,0.2);
                text-align: center;
            }
            
            ul li:last-child {
                border-bottom: none;
            }
            
            #menu:checked + label + ul {
                display: flex;
                animation: slideDown 0.3s ease-in-out;
            }
        }
        
        @media (max-width: 500px) {
            nav {
                padding: 10px 15px;
            }
            
            .logo {
                height: 1.8rem;
                width: 1.8rem;
            }
            
            ul {
                top: 55px;
            }
            
            ul li {
                padding: 10px 15px;
            }
        }
        
        /* Animation for menu opening */
        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <nav>
        <img class="logo" src="images/download.png" alt="Logo">
        <!-- Hidden checkbox -->
        <input type="checkbox" id="menu">
        <label for="menu" class="hamburger">
            <span></span>
            <span></span>
            <span></span>
        </label>
        <ul>
            <li>Home</li>
            <li>Church</li>
            <li>Mall</li>
            <li>School</li>
        </ul>
    </nav>
    
    <div class="content">
        <img class="img" src="images/download.png" alt="Content image">
        <h1>Manchester United</h1>
        <h2>Comedy of Errors</h2>
    </div>
</body>
</html>