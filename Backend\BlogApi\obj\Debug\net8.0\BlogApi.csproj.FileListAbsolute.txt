C:\Users\<USER>\Desktop\Backend\BlogApi\bin\Debug\net8.0\appsettings.Development.json
C:\Users\<USER>\Desktop\Backend\BlogApi\bin\Debug\net8.0\appsettings.json
C:\Users\<USER>\Desktop\Backend\BlogApi\bin\Debug\net8.0\BlogApi.exe
C:\Users\<USER>\Desktop\Backend\BlogApi\bin\Debug\net8.0\BlogApi.deps.json
C:\Users\<USER>\Desktop\Backend\BlogApi\bin\Debug\net8.0\BlogApi.runtimeconfig.json
C:\Users\<USER>\Desktop\Backend\BlogApi\bin\Debug\net8.0\BlogApi.dll
C:\Users\<USER>\Desktop\Backend\BlogApi\bin\Debug\net8.0\BlogApi.pdb
C:\Users\<USER>\Desktop\Backend\BlogApi\bin\Debug\net8.0\Microsoft.AspNetCore.OpenApi.dll
C:\Users\<USER>\Desktop\Backend\BlogApi\bin\Debug\net8.0\Microsoft.OpenApi.dll
C:\Users\<USER>\Desktop\Backend\BlogApi\bin\Debug\net8.0\Swashbuckle.AspNetCore.Swagger.dll
C:\Users\<USER>\Desktop\Backend\BlogApi\bin\Debug\net8.0\Swashbuckle.AspNetCore.SwaggerGen.dll
C:\Users\<USER>\Desktop\Backend\BlogApi\bin\Debug\net8.0\Swashbuckle.AspNetCore.SwaggerUI.dll
C:\Users\<USER>\Desktop\Backend\BlogApi\obj\Debug\net8.0\BlogApi.csproj.AssemblyReference.cache
C:\Users\<USER>\Desktop\Backend\BlogApi\obj\Debug\net8.0\BlogApi.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\Desktop\Backend\BlogApi\obj\Debug\net8.0\BlogApi.AssemblyInfoInputs.cache
C:\Users\<USER>\Desktop\Backend\BlogApi\obj\Debug\net8.0\BlogApi.AssemblyInfo.cs
C:\Users\<USER>\Desktop\Backend\BlogApi\obj\Debug\net8.0\BlogApi.csproj.CoreCompileInputs.cache
C:\Users\<USER>\Desktop\Backend\BlogApi\obj\Debug\net8.0\BlogApi.MvcApplicationPartsAssemblyInfo.cs
C:\Users\<USER>\Desktop\Backend\BlogApi\obj\Debug\net8.0\BlogApi.MvcApplicationPartsAssemblyInfo.cache
C:\Users\<USER>\Desktop\Backend\BlogApi\obj\Debug\net8.0\staticwebassets.build.json
C:\Users\<USER>\Desktop\Backend\BlogApi\obj\Debug\net8.0\staticwebassets.development.json
C:\Users\<USER>\Desktop\Backend\BlogApi\obj\Debug\net8.0\staticwebassets\msbuild.BlogApi.Microsoft.AspNetCore.StaticWebAssets.props
C:\Users\<USER>\Desktop\Backend\BlogApi\obj\Debug\net8.0\staticwebassets\msbuild.build.BlogApi.props
C:\Users\<USER>\Desktop\Backend\BlogApi\obj\Debug\net8.0\staticwebassets\msbuild.buildMultiTargeting.BlogApi.props
C:\Users\<USER>\Desktop\Backend\BlogApi\obj\Debug\net8.0\staticwebassets\msbuild.buildTransitive.BlogApi.props
C:\Users\<USER>\Desktop\Backend\BlogApi\obj\Debug\net8.0\staticwebassets.pack.json
C:\Users\<USER>\Desktop\Backend\BlogApi\obj\Debug\net8.0\scopedcss\bundle\BlogApi.styles.css
C:\Users\<USER>\Desktop\Backend\BlogApi\obj\Debug\net8.0\BlogApi.csproj.Up2Date
C:\Users\<USER>\Desktop\Backend\BlogApi\obj\Debug\net8.0\BlogApi.dll
C:\Users\<USER>\Desktop\Backend\BlogApi\obj\Debug\net8.0\refint\BlogApi.dll
C:\Users\<USER>\Desktop\Backend\BlogApi\obj\Debug\net8.0\BlogApi.pdb
C:\Users\<USER>\Desktop\Backend\BlogApi\obj\Debug\net8.0\BlogApi.genruntimeconfig.cache
C:\Users\<USER>\Desktop\Backend\BlogApi\obj\Debug\net8.0\ref\BlogApi.dll
C:\Users\<USER>\Desktop\Api Hub\Backend\BlogApi\bin\Debug\net8.0\appsettings.Development.json
C:\Users\<USER>\Desktop\Api Hub\Backend\BlogApi\bin\Debug\net8.0\appsettings.json
C:\Users\<USER>\Desktop\Api Hub\Backend\BlogApi\bin\Debug\net8.0\BlogApi.exe
C:\Users\<USER>\Desktop\Api Hub\Backend\BlogApi\bin\Debug\net8.0\BlogApi.deps.json
C:\Users\<USER>\Desktop\Api Hub\Backend\BlogApi\bin\Debug\net8.0\BlogApi.runtimeconfig.json
C:\Users\<USER>\Desktop\Api Hub\Backend\BlogApi\bin\Debug\net8.0\BlogApi.dll
C:\Users\<USER>\Desktop\Api Hub\Backend\BlogApi\bin\Debug\net8.0\BlogApi.pdb
C:\Users\<USER>\Desktop\Api Hub\Backend\BlogApi\bin\Debug\net8.0\Microsoft.AspNetCore.OpenApi.dll
C:\Users\<USER>\Desktop\Api Hub\Backend\BlogApi\bin\Debug\net8.0\Microsoft.OpenApi.dll
C:\Users\<USER>\Desktop\Api Hub\Backend\BlogApi\bin\Debug\net8.0\Swashbuckle.AspNetCore.Swagger.dll
C:\Users\<USER>\Desktop\Api Hub\Backend\BlogApi\bin\Debug\net8.0\Swashbuckle.AspNetCore.SwaggerGen.dll
C:\Users\<USER>\Desktop\Api Hub\Backend\BlogApi\bin\Debug\net8.0\Swashbuckle.AspNetCore.SwaggerUI.dll
C:\Users\<USER>\Desktop\Api Hub\Backend\BlogApi\obj\Debug\net8.0\BlogApi.csproj.AssemblyReference.cache
C:\Users\<USER>\Desktop\Api Hub\Backend\BlogApi\obj\Debug\net8.0\BlogApi.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\Desktop\Api Hub\Backend\BlogApi\obj\Debug\net8.0\BlogApi.AssemblyInfoInputs.cache
C:\Users\<USER>\Desktop\Api Hub\Backend\BlogApi\obj\Debug\net8.0\BlogApi.AssemblyInfo.cs
C:\Users\<USER>\Desktop\Api Hub\Backend\BlogApi\obj\Debug\net8.0\BlogApi.csproj.CoreCompileInputs.cache
C:\Users\<USER>\Desktop\Api Hub\Backend\BlogApi\obj\Debug\net8.0\BlogApi.MvcApplicationPartsAssemblyInfo.cs
C:\Users\<USER>\Desktop\Api Hub\Backend\BlogApi\obj\Debug\net8.0\BlogApi.MvcApplicationPartsAssemblyInfo.cache
C:\Users\<USER>\Desktop\Api Hub\Backend\BlogApi\obj\Debug\net8.0\staticwebassets.build.json
C:\Users\<USER>\Desktop\Api Hub\Backend\BlogApi\obj\Debug\net8.0\staticwebassets.development.json
C:\Users\<USER>\Desktop\Api Hub\Backend\BlogApi\obj\Debug\net8.0\staticwebassets\msbuild.BlogApi.Microsoft.AspNetCore.StaticWebAssets.props
C:\Users\<USER>\Desktop\Api Hub\Backend\BlogApi\obj\Debug\net8.0\staticwebassets\msbuild.build.BlogApi.props
C:\Users\<USER>\Desktop\Api Hub\Backend\BlogApi\obj\Debug\net8.0\staticwebassets\msbuild.buildMultiTargeting.BlogApi.props
C:\Users\<USER>\Desktop\Api Hub\Backend\BlogApi\obj\Debug\net8.0\staticwebassets\msbuild.buildTransitive.BlogApi.props
C:\Users\<USER>\Desktop\Api Hub\Backend\BlogApi\obj\Debug\net8.0\staticwebassets.pack.json
C:\Users\<USER>\Desktop\Api Hub\Backend\BlogApi\obj\Debug\net8.0\scopedcss\bundle\BlogApi.styles.css
C:\Users\<USER>\Desktop\Api Hub\Backend\BlogApi\obj\Debug\net8.0\BlogApi.csproj.Up2Date
C:\Users\<USER>\Desktop\Api Hub\Backend\BlogApi\obj\Debug\net8.0\BlogApi.dll
C:\Users\<USER>\Desktop\Api Hub\Backend\BlogApi\obj\Debug\net8.0\refint\BlogApi.dll
C:\Users\<USER>\Desktop\Api Hub\Backend\BlogApi\obj\Debug\net8.0\BlogApi.pdb
C:\Users\<USER>\Desktop\Api Hub\Backend\BlogApi\obj\Debug\net8.0\BlogApi.genruntimeconfig.cache
C:\Users\<USER>\Desktop\Api Hub\Backend\BlogApi\obj\Debug\net8.0\ref\BlogApi.dll
