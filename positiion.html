<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>

    <style>
        button{
            background-color: red;
            color: white;
            border: none;
            padding: 15px 32px;
            border-radius: 5px;
            transition: all 1s;
        }
        button:hover{
            transform: scale(1.05);
             box-shadow: 0 5px 15px rgba(0,0,0,0.2);
             background-color: white;
             color: red;
        }
        button:active{
            background-color: green;

        }
        button:focus{
            background-color: blue;
        }
        input[type="checkbox"]:checked{
            scale: 1.5;
            background-color: rgb(109, 109, 145);

        }
        button.loading::after{
            content: "";
            border-right: 2px solid white;
            
            border: 0px 2px solid white;
            /* border-top-color:transparent; */
            border-radius: 50%;
            width: 12px;
            height: 12px;
            display: inline-block;
            margin-left: 8px;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            to {
                transform: rotate(360deg);
            }            
        }

    </style>

</head>
<body>

    <button>Click Me</button>
    <button class="loading"></button>
    <input type="checkbox" name="" id="">
</body>
</html>