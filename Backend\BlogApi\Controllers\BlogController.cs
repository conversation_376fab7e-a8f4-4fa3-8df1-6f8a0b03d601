using BlogApi.DTOs;
using BlogApi.Models;
using BlogApi.Services.Interface;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.VisualBasic;

namespace BlogApi.Controllers;
[ApiController]
[Route("api/[controller]")]
public class BlogController : ControllerBase
{
private readonly IBlogRepository _blogRepository;

    public BlogController(IBlogRepository blogRepository)
    {
        _blogRepository = blogRepository;
    }



    //Endpoint to create a new blog
    [HttpPost]
public async Task<IActionResult> CreateBlog([FromBody] CreatedBlogRequestDTOs request)
{
    var newBlog = await _blogRepository.CreateBlogAsync(request);
    return Ok(newBlog);
}
 

//Endpoint to fetch all Blogs
 [HttpGet]
public async Task<IActionResult> GetAllBlogs(){
    var  allBlogs = await _blogRepository.GetAllBlogAsync();
    return Ok(allBlogs);
}


//Endpoint to fetch post by ID
[HttpGet("{id}")]
public async Task<IActionResult> GetBlogById(Guid id)
{
   var targetBlog = await _blogRepository.GetBlogByIdAsync(id);
   return Ok(targetBlog);
    }




//End point to update a blog
[HttpPut("{id}")]
public async Task<IActionResult> UpdateBlog(Guid id, UpdateBlogRequestDTOs request)
{   
    var targetBlog = await _blogRepository.UpdatedBlogByIdAsync(id,request);
    return Ok(targetBlog);
}

//EndPoint to Delete Blog By Id
[HttpDelete("{id}")]
public async Task<IActionResult> DeleteBlogById(Guid id){
    await _blogRepository.DeleteBlogByIdAsync(id);
     return NoContent();

}

//Endpoint to delete Blog
[HttpDelete]
public async Task<IActionResult> DeleteAllBlog(){
 await   _blogRepository.DeleteAllAsync();
    return NoContent();
}

}