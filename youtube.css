
.container{
    padding-left: 0;
    padding-top: 1rem;
    width: 18rem;
    display: inline-block;
    background-color: white;
    border-radius: 2rem;

}
.img{
    height: 11rem;
    width: 17rem;
    border-radius: 0.55rem;

}
.profile-details{
    width: 15rem;
}
.img-container{
margin-bottom: 0.5rem;
padding-right: 1rem;
margin-right: 0;

}
.profile-img{
height: 3rem;
width: 3rem;
border-radius: 1.5rem;
}
.profile-img-container{
    display: flex;
    width: 18rem;
    margin-bottom: 0.5rem;

}
.video-title{
    font-size: 14px;
    margin-left: 1rem;
}

.video-description{
    
    width: 100%;
    color:gray;
}
.video-description-1{
    margin-top: 0;
    margin-bottom: 0px;
    padding-left: 5rem;
    padding-right: 0rem;
    
}
.video-description-2{
    margin-top: 0.5rem;
    padding-left: 5rem;
}
.first-cont{
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    flex-wrap: wrap-reverse;
}



.search-bar{
    display: block;

}
.header{
    height: 55px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;

    background-color: white;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;

    border-bottom-width:1px ;
    border-style: solid;
    border-bottom-color: rgb(228, 228, 228) ;
    z-index: 200;



}
.left-section{
    display: flex;
    align-items: center;
}
.middle-section{
    flex: 1;
    display: flex;
    align-items: center;
    margin-left: 70px;
    max-width: 500px;
margin-right: 35px;
}


.search-bar{
    flex: 1;
    height: 34px;
    padding-left: 10px;
    font-size: 16px;
    border-style: solid;
    border-width: 1px;
    border-radius: 2px;
    border-color: grey;
    box-shadow: inset 1px 2px 3px rgb(0, 0, 0,0.05);
    width: 0px;
}

.search-btn{
   height: 38px;
   width: 66px;
    background-color: rgb(240, 240, 240);
     border-width: 1px;
    border-style: solid;
    border-color: rgb(192, 192, 192);
    margin-left: -1px;
    margin-right: 8px;
    position: relative;
}


.search-icon{
   height: 25px;
   margin-top: 4px;
}

.voice-search-icon{
    height: 25px;
    margin-top:4px ;
}
.voice-search-btn{
    height: 40px;
    width: 40px;
    border-radius: 20px;
    border: none;
    background-color: rgb(245, 245, 245);
}
.right-section{
    width: 180px;
    display: flex;
    align-items: center;
    justify-content: space-between;  
    flex-shrink: 0;
}
.hamburger-menu{
    height: 20px;
    margin-left: 24px;
    margin-right: 20px;
}
.youtube-logo{
    height: 20px;
}

.search-bar::placeholder{
    font-family: 'Times New Roman', Times, serif;
    font-size: 16px ;
}
.upload-icon{
    height: 24px;
}
.youtube-apps-icon{
    height: 24px;
}
.notifications-icon{
    height: 24px;
    aspect-ratio: 1 ;
}
.current-picture{
    height: 32px;
    border-radius: 16px;
    margin-right: 20px;
}

.notifications-icon-container{
    position: relative;
}
.notifications-count{
    position: absolute;
    background-color: red;
    top: -7px;
    right: -9px;
    width: 20px;
    aspect-ratio: 1;
    display: grid;
    place-items: center;
    border-radius: 50%;
    
    font-size: 10px;
    color: white;
   
}
.first-row{
    margin-top: 3rem;
}
.sidebar{
    position: fixed;
    left: 0;
    bottom: 0;
    top: 55px;
    background-color: white;
    width: 72px;
    z-index: 200;
    padding-top: 5px;

   
}
.sidebar-link{
height: 74px;
display: flex;
justify-content: center;
align-items: center;
flex-direction: column;
cursor: pointer;

}
.sidebar-link:hover{
    background-color: rgb(235, 235, 235);
}
.sidebar-link img{
    height: 24px;
    margin-bottom: 4px;
}
.sidebar-link div{
font-size: 10px;
}
.main-body{
    margin-left: 10rem;
}
.container-box{
    display: grid;
    
}