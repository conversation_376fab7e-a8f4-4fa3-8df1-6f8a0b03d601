using BlogApi.Models;
using BlogApi.DTOs;
using BlogApi.Services.Interface;

namespace BlogApi.Services.Repository;

public class BlogRepository : IBlogRepository
{
    private static List<Blog> _blogList = new List<Blog>();
    
// create a new blog
    public async Task<Blog> CreateBlogAsync(CreatedBlogRequestDTOs request)
    {
        var newBlog = new Blog{
            Title =request.Title,
            Content = request.Content,
            Author = request.Author,
            CreatedAt = DateTime.UtcNow.ToString(),

        };
        _blogList.Add(newBlog);
        return await Task.FromResult(newBlog);
    }

    public Task<bool> DeleteAllAsync()
    {
        foreach (var blog in _blogList)
        {
            blog.DeletedAt = DateTime.UtcNow.ToString();
        }
        return Task.FromResult(true);
        
    }

    // Delete all blogs
    public  async Task<bool> DeleteAllBlogsAsync()
    {
         _blogList.Clear();
        return await Task.FromResult(true);
    }

//  delete a blog by ID
    public async Task<bool> DeleteBlogByIdAsync(Guid Id)
    {
         var newBlog = _blogList.FirstOrDefault(b => b.Id ==Id && b.DeletedAt == null);
    
        if (newBlog == null){
            return await Task.FromResult(false);
        }
        newBlog.DeletedAt = DateTime.UtcNow.ToString();
        return await Task.FromResult(true);

    
    }

//  fetch all blogs
     public async Task<List<Blog>> GetAllBlogAsync()
     {
         var newBlog = _blogList.Where(b => b.DeletedAt == null).ToList();
         return await Task.FromResult(newBlog);

     }

// fetch a blog by ID
    public async Task<Blog> GetBlogByIdAsync(Guid Id)
    {
        var newBlog = _blogList.FirstOrDefault(b => b.Id == Id && b.DeletedAt ==null);
    if (newBlog == null)
    {
     throw new Exception("Blog not found");
    }
    newBlog.DeletedAt = DateTime.UtcNow.ToString();
    return await Task.FromResult(newBlog);
    }

    

    // Endpoint to update a blog
    public  Task<Blog> UpdatedBlogByIdAsync(Guid Id, UpdateBlogRequestDTOs request)
    {
            var newBlog = _blogList.FirstOrDefault(b => b.Id == Id && b.DeletedAt ==null);
    if (newBlog == null) 
    {
        throw new Exception("Blog not found");
    }
    newBlog.Title = request.Title ?? newBlog.Title;
    newBlog.Content = request.Content ?? newBlog.Content;
    newBlog.Author = request.Author ?? newBlog.Author;
    newBlog.UpdatedAt = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss");
    
    return  Task.FromResult(newBlog);
    }

}
