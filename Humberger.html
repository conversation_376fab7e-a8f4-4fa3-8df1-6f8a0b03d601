<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<style>
    *{
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
.navbar{
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: black;
  height: 50px;
}

.nav-links{
  display: flex;
  list-style: none;
  gap: 20px;
}

.nav-links li a{
  font-size: 18px;
  color: white;
}

.logo{
  font-size: 24px;
  color: white;
}

.humburger{
  display: none;
  flex-direction: column;
  cursor: pointer;
  border: 1px solid #fff;
  padding: 5px;
  border-radius: 5px;
}

.humburger-line{
  width: 30px;
  height: 3px;
  background-color: #fff;
  margin: 4px 0;
  transition: 0.4s;
}

#humburger-toggle{
  display: none;
}

@media(max-width: 500px){
  .nav-links{
    display: none;
    flex-direction: column;
    position: absolute;
    top: 60px;
    left: 0;
    right: 0;
    background-color: #333;
    padding: 10px
  }

  .humburger{
    display: flex;
  }

  #humburger-toggle{
    display: block;
    position: absolute;
    top: 22px;   
    right: 31px;
    opacity: 0; 
  }

  #humburger-toggle:checked + .nav-links {
    display: flex;
  }
  
}
</style>
<body>
    <nav class="navbar">
        <div class="logo">
            <span>Logo</span>
        </div>
        <input type="checkbox" class="humberger-checkbox" id="humberger-toggle"> 
        <div class="humberger">
            <span class="humberger-line"></span>
            <span class="humberger-line"></span>
            <span class="humberger-line"></span>
        </div>

        <ul class="nav-links">
            <li><a href="#">Home</a></li>
            <li><a href="#">Services</a></li>
            <li><a href="#">About</a></li>
            <li><a href="#">Services</a></li>
            

        </ul>
    </nav>
</body>
</html>