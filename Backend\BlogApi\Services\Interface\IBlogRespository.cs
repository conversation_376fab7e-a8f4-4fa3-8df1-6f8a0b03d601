using BlogApi.DTOs;
using BlogApi.Models;


namespace BlogApi.Services.Interface;


public interface IBlogRepository
{
    Task<Blog> CreateBlogAsync(CreatedBlogRequestDTOs request);
    Task <List<Blog>> GetAllBlogAsync();
    Task<Blog> GetBlogByIdAsync(Guid Id);
    Task<Blog> UpdatedBlogByIdAsync(Guid Id, UpdateBlogRequestDTOs request);
    Task<bool> DeleteBlogByIdAsync (Guid Id);
    Task<bool> DeleteAllAsync ();
}

